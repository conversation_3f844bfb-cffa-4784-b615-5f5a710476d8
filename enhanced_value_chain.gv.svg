<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: Enhanced_ValueChain Pages: 1 -->
<svg width="1046pt" height="819pt"
 viewBox="0.00 0.00 1046.00 819.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 815)">
<title>Enhanced_ValueChain</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-815 1042,-815 1042,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_support</title>
<polygon fill="lightblue" stroke="lightblue" points="8,-698 8,-803 647,-803 647,-698 8,-698"/>
<text text-anchor="middle" x="327.5" y="-786.2" font-family="Arial Bold" font-size="16.00">Support Activities</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_primary</title>
<polygon fill="lightgreen" stroke="lightgreen" points="655,-146 655,-797.5 806,-797.5 806,-146 655,-146"/>
<text text-anchor="middle" x="730.5" y="-780.7" font-family="Arial Bold" font-size="16.00">Primary Activities</text>
</g>
<!-- Firm Infrastructure -->
<g id="node1" class="node">
<title>Firm Infrastructure</title>
<path fill="#e8f4fd" stroke="black" d="M626.5,-769C626.5,-769 537.5,-769 537.5,-769 531.5,-769 525.5,-763 525.5,-757 525.5,-757 525.5,-718 525.5,-718 525.5,-712 531.5,-706 537.5,-706 537.5,-706 626.5,-706 626.5,-706 632.5,-706 638.5,-712 638.5,-718 638.5,-718 638.5,-757 638.5,-757 638.5,-763 632.5,-769 626.5,-769"/>
<text text-anchor="middle" x="582" y="-757" font-family="Arial" font-size="10.00">Firm Infrastructure</text>
<text text-anchor="middle" x="582" y="-746" font-family="Arial" font-size="10.00">• Planning</text>
<text text-anchor="middle" x="582" y="-735" font-family="Arial" font-size="10.00">• Finance</text>
<text text-anchor="middle" x="582" y="-724" font-family="Arial" font-size="10.00">• Legal</text>
<text text-anchor="middle" x="582" y="-713" font-family="Arial" font-size="10.00">• Quality Management</text>
</g>
<!-- Inbound_Logistics -->
<g id="node5" class="node">
<title>Inbound_Logistics</title>
<path fill="#e8f8e8" stroke="black" d="M786,-763.5C786,-763.5 712,-763.5 712,-763.5 706,-763.5 700,-757.5 700,-751.5 700,-751.5 700,-723.5 700,-723.5 700,-717.5 706,-711.5 712,-711.5 712,-711.5 786,-711.5 786,-711.5 792,-711.5 798,-717.5 798,-723.5 798,-723.5 798,-751.5 798,-751.5 798,-757.5 792,-763.5 786,-763.5"/>
<text text-anchor="middle" x="749" y="-751.5" font-family="Arial" font-size="10.00">Inbound Logistics</text>
<text text-anchor="middle" x="749" y="-740.5" font-family="Arial" font-size="10.00">• Receiving</text>
<text text-anchor="middle" x="749" y="-729.5" font-family="Arial" font-size="10.00">• Warehousing</text>
<text text-anchor="middle" x="749" y="-718.5" font-family="Arial" font-size="10.00">• Inventory Control</text>
</g>
<!-- Firm Infrastructure&#45;&gt;Inbound_Logistics -->
<g id="edge5" class="edge">
<title>Firm Infrastructure&#45;&gt;Inbound_Logistics</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M638.54,-737C638.54,-737 691.78,-737 691.78,-737"/>
<polygon fill="blue" stroke="blue" points="691.78,-739.8 699.78,-737 691.78,-734.2 691.78,-739.8"/>
</g>
<!-- Outbound_Logistics -->
<g id="node7" class="node">
<title>Outbound_Logistics</title>
<path fill="#e8f8e8" stroke="black" d="M785.5,-482C785.5,-482 708.5,-482 708.5,-482 702.5,-482 696.5,-476 696.5,-470 696.5,-470 696.5,-442 696.5,-442 696.5,-436 702.5,-430 708.5,-430 708.5,-430 785.5,-430 785.5,-430 791.5,-430 797.5,-436 797.5,-442 797.5,-442 797.5,-470 797.5,-470 797.5,-476 791.5,-482 785.5,-482"/>
<text text-anchor="middle" x="747" y="-470" font-family="Arial" font-size="10.00">Outbound Logistics</text>
<text text-anchor="middle" x="747" y="-459" font-family="Arial" font-size="10.00">• Order Processing</text>
<text text-anchor="middle" x="747" y="-448" font-family="Arial" font-size="10.00">• Shipping</text>
<text text-anchor="middle" x="747" y="-437" font-family="Arial" font-size="10.00">• Distribution</text>
</g>
<!-- Firm Infrastructure&#45;&gt;Outbound_Logistics -->
<g id="edge6" class="edge">
<title>Firm Infrastructure&#45;&gt;Outbound_Logistics</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M582,-705.86C582,-633.87 582,-464 582,-464 582,-464 688.15,-464 688.15,-464"/>
<polygon fill="blue" stroke="blue" points="688.15,-466.8 696.15,-464 688.15,-461.2 688.15,-466.8"/>
</g>
<!-- HR Management -->
<g id="node2" class="node">
<title>HR Management</title>
<path fill="#e8f4fd" stroke="black" d="M455.5,-769C455.5,-769 388.5,-769 388.5,-769 382.5,-769 376.5,-763 376.5,-757 376.5,-757 376.5,-718 376.5,-718 376.5,-712 382.5,-706 388.5,-706 388.5,-706 455.5,-706 455.5,-706 461.5,-706 467.5,-712 467.5,-718 467.5,-718 467.5,-757 467.5,-757 467.5,-763 461.5,-769 455.5,-769"/>
<text text-anchor="middle" x="422" y="-757" font-family="Arial" font-size="10.00">HR Management</text>
<text text-anchor="middle" x="422" y="-746" font-family="Arial" font-size="10.00">• Recruiting</text>
<text text-anchor="middle" x="422" y="-735" font-family="Arial" font-size="10.00">• Training</text>
<text text-anchor="middle" x="422" y="-724" font-family="Arial" font-size="10.00">• Development</text>
<text text-anchor="middle" x="422" y="-713" font-family="Arial" font-size="10.00">• Compensation</text>
</g>
<!-- Operations -->
<g id="node6" class="node">
<title>Operations</title>
<path fill="#e8f8e8" stroke="black" d="M781.5,-620C781.5,-620 716.5,-620 716.5,-620 710.5,-620 704.5,-614 704.5,-608 704.5,-608 704.5,-580 704.5,-580 704.5,-574 710.5,-568 716.5,-568 716.5,-568 781.5,-568 781.5,-568 787.5,-568 793.5,-574 793.5,-580 793.5,-580 793.5,-608 793.5,-608 793.5,-614 787.5,-620 781.5,-620"/>
<text text-anchor="middle" x="749" y="-608" font-family="Arial" font-size="10.00">Operations</text>
<text text-anchor="middle" x="749" y="-597" font-family="Arial" font-size="10.00">• Manufacturing</text>
<text text-anchor="middle" x="749" y="-586" font-family="Arial" font-size="10.00">• Assembly</text>
<text text-anchor="middle" x="749" y="-575" font-family="Arial" font-size="10.00">• Quality Control</text>
</g>
<!-- HR Management&#45;&gt;Operations -->
<g id="edge7" class="edge">
<title>HR Management&#45;&gt;Operations</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M437.17,-705.91C437.17,-667.29 437.17,-607 437.17,-607 437.17,-607 696.32,-607 696.32,-607"/>
<polygon fill="blue" stroke="blue" points="696.32,-609.8 704.32,-607 696.32,-604.2 696.32,-609.8"/>
</g>
<!-- Service -->
<g id="node9" class="node">
<title>Service</title>
<path fill="#e8f8e8" stroke="black" d="M754.5,-206C754.5,-206 675.5,-206 675.5,-206 669.5,-206 663.5,-200 663.5,-194 663.5,-194 663.5,-166 663.5,-166 663.5,-160 669.5,-154 675.5,-154 675.5,-154 754.5,-154 754.5,-154 760.5,-154 766.5,-160 766.5,-166 766.5,-166 766.5,-194 766.5,-194 766.5,-200 760.5,-206 754.5,-206"/>
<text text-anchor="middle" x="715" y="-194" font-family="Arial" font-size="10.00">Service</text>
<text text-anchor="middle" x="715" y="-183" font-family="Arial" font-size="10.00">• Installation</text>
<text text-anchor="middle" x="715" y="-172" font-family="Arial" font-size="10.00">• Repair</text>
<text text-anchor="middle" x="715" y="-161" font-family="Arial" font-size="10.00">• Customer Support</text>
</g>
<!-- HR Management&#45;&gt;Service -->
<g id="edge8" class="edge">
<title>HR Management&#45;&gt;Service</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M406.83,-705.98C406.83,-589.22 406.83,-188 406.83,-188 406.83,-188 655.42,-188 655.42,-188"/>
<polygon fill="blue" stroke="blue" points="655.42,-190.8 663.42,-188 655.42,-185.2 655.42,-190.8"/>
</g>
<!-- Technology Development -->
<g id="node3" class="node">
<title>Technology Development</title>
<path fill="#e8f4fd" stroke="black" d="M306.5,-763.5C306.5,-763.5 203.5,-763.5 203.5,-763.5 197.5,-763.5 191.5,-757.5 191.5,-751.5 191.5,-751.5 191.5,-723.5 191.5,-723.5 191.5,-717.5 197.5,-711.5 203.5,-711.5 203.5,-711.5 306.5,-711.5 306.5,-711.5 312.5,-711.5 318.5,-717.5 318.5,-723.5 318.5,-723.5 318.5,-751.5 318.5,-751.5 318.5,-757.5 312.5,-763.5 306.5,-763.5"/>
<text text-anchor="middle" x="255" y="-751.5" font-family="Arial" font-size="10.00">Technology Development</text>
<text text-anchor="middle" x="255" y="-740.5" font-family="Arial" font-size="10.00">• R&amp;D</text>
<text text-anchor="middle" x="255" y="-729.5" font-family="Arial" font-size="10.00">• Product Design</text>
<text text-anchor="middle" x="255" y="-718.5" font-family="Arial" font-size="10.00">• Process Automation</text>
</g>
<!-- Technology Development&#45;&gt;Operations -->
<g id="edge9" class="edge">
<title>Technology Development&#45;&gt;Operations</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M276.17,-711.18C276.17,-669.75 276.17,-594 276.17,-594 276.17,-594 696.09,-594 696.09,-594"/>
<polygon fill="blue" stroke="blue" points="696.09,-596.8 704.09,-594 696.09,-591.2 696.09,-596.8"/>
</g>
<!-- Marketing_and_Sales -->
<g id="node8" class="node">
<title>Marketing_and_Sales</title>
<path fill="#e8f8e8" stroke="black" d="M786,-344C786,-344 706,-344 706,-344 700,-344 694,-338 694,-332 694,-332 694,-304 694,-304 694,-298 700,-292 706,-292 706,-292 786,-292 786,-292 792,-292 798,-298 798,-304 798,-304 798,-332 798,-332 798,-338 792,-344 786,-344"/>
<text text-anchor="middle" x="746" y="-332" font-family="Arial" font-size="10.00">Marketing &amp; Sales</text>
<text text-anchor="middle" x="746" y="-321" font-family="Arial" font-size="10.00">• Advertising</text>
<text text-anchor="middle" x="746" y="-310" font-family="Arial" font-size="10.00">• Sales Force</text>
<text text-anchor="middle" x="746" y="-299" font-family="Arial" font-size="10.00">• Channel Relations</text>
</g>
<!-- Technology Development&#45;&gt;Marketing_and_Sales -->
<g id="edge10" class="edge">
<title>Technology Development&#45;&gt;Marketing_and_Sales</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M233.83,-711.45C233.83,-620.99 233.83,-326 233.83,-326 233.83,-326 685.6,-326 685.6,-326"/>
<polygon fill="blue" stroke="blue" points="685.6,-328.8 693.6,-326 685.6,-323.2 685.6,-328.8"/>
</g>
<!-- Procurement -->
<g id="node4" class="node">
<title>Procurement</title>
<path fill="#e8f4fd" stroke="black" d="M121.5,-763.5C121.5,-763.5 28.5,-763.5 28.5,-763.5 22.5,-763.5 16.5,-757.5 16.5,-751.5 16.5,-751.5 16.5,-723.5 16.5,-723.5 16.5,-717.5 22.5,-711.5 28.5,-711.5 28.5,-711.5 121.5,-711.5 121.5,-711.5 127.5,-711.5 133.5,-717.5 133.5,-723.5 133.5,-723.5 133.5,-751.5 133.5,-751.5 133.5,-757.5 127.5,-763.5 121.5,-763.5"/>
<text text-anchor="middle" x="75" y="-751.5" font-family="Arial" font-size="10.00">Procurement</text>
<text text-anchor="middle" x="75" y="-740.5" font-family="Arial" font-size="10.00">• Purchasing</text>
<text text-anchor="middle" x="75" y="-729.5" font-family="Arial" font-size="10.00">• Supplier Relations</text>
<text text-anchor="middle" x="75" y="-718.5" font-family="Arial" font-size="10.00">• Material Management</text>
</g>
<!-- Procurement&#45;&gt;Inbound_Logistics -->
<g id="edge11" class="edge">
<title>Procurement&#45;&gt;Inbound_Logistics</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M75,-763.7C75,-775.57 75,-787 75,-787 75,-787 749,-787 749,-787 749,-787 749,-771.7 749,-771.7"/>
<polygon fill="blue" stroke="blue" points="751.8,-771.7 749,-763.7 746.2,-771.7 751.8,-771.7"/>
</g>
<!-- Procurement&#45;&gt;Operations -->
<g id="edge12" class="edge">
<title>Procurement&#45;&gt;Operations</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M110.1,-711.31C110.1,-666.75 110.1,-581 110.1,-581 110.1,-581 696.33,-581 696.33,-581"/>
<polygon fill="blue" stroke="blue" points="696.33,-583.8 704.33,-581 696.33,-578.2 696.33,-583.8"/>
</g>
<!-- Procurement&#45;&gt;Outbound_Logistics -->
<g id="edge13" class="edge">
<title>Procurement&#45;&gt;Outbound_Logistics</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M86.7,-711.46C86.7,-640.16 86.7,-447 86.7,-447 86.7,-447 688.28,-447 688.28,-447"/>
<polygon fill="blue" stroke="blue" points="688.28,-449.8 696.28,-447 688.28,-444.2 688.28,-449.8"/>
</g>
<!-- Procurement&#45;&gt;Marketing_and_Sales -->
<g id="edge14" class="edge">
<title>Procurement&#45;&gt;Marketing_and_Sales</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M63.3,-711.34C63.3,-618.31 63.3,-309 63.3,-309 63.3,-309 685.85,-309 685.85,-309"/>
<polygon fill="blue" stroke="blue" points="685.85,-311.8 693.85,-309 685.85,-306.2 685.85,-311.8"/>
</g>
<!-- Procurement&#45;&gt;Service -->
<g id="edge15" class="edge">
<title>Procurement&#45;&gt;Service</title>
<path fill="none" stroke="blue" stroke-dasharray="5,2" d="M39.9,-711.31C39.9,-599.9 39.9,-171 39.9,-171 39.9,-171 655.43,-171 655.43,-171"/>
<polygon fill="blue" stroke="blue" points="655.43,-173.8 663.43,-171 655.43,-168.2 655.43,-173.8"/>
</g>
<!-- Inbound_Logistics&#45;&gt;Operations -->
<g id="edge1" class="edge">
<title>Inbound_Logistics&#45;&gt;Operations</title>
<path fill="none" stroke="darkgreen" stroke-width="2" d="M749,-711.18C749,-711.18 749,-632.15 749,-632.15"/>
<polygon fill="darkgreen" stroke="darkgreen" stroke-width="2" points="753.2,-632.15 749,-620.15 744.8,-632.15 753.2,-632.15"/>
</g>
<!-- MARGIN -->
<g id="node10" class="node">
<title>MARGIN</title>
<polygon fill="gold" stroke="black" points="973,-68 908,-34 973,0 1038,-34 973,-68"/>
<text text-anchor="middle" x="973" y="-37.4" font-family="Arial Bold" font-size="12.00">MARGIN</text>
<text text-anchor="middle" x="973" y="-24.4" font-family="Arial Bold" font-size="12.00">(Profit)</text>
</g>
<!-- Inbound_Logistics&#45;&gt;MARGIN -->
<g id="edge16" class="edge">
<title>Inbound_Logistics&#45;&gt;MARGIN</title>
<path fill="none" stroke="orange" stroke-width="2" d="M798.15,-737C872.63,-737 1005.5,-737 1005.5,-737 1005.5,-737 1005.5,-61.03 1005.5,-61.03"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1009,-61.03 1005.5,-51.03 1002,-61.03 1009,-61.03"/>
</g>
<!-- Operations&#45;&gt;Outbound_Logistics -->
<g id="edge2" class="edge">
<title>Operations&#45;&gt;Outbound_Logistics</title>
<path fill="none" stroke="darkgreen" stroke-width="2" d="M749,-567.72C749,-567.72 749,-494.18 749,-494.18"/>
<polygon fill="darkgreen" stroke="darkgreen" stroke-width="2" points="753.2,-494.18 749,-482.18 744.8,-494.18 753.2,-494.18"/>
</g>
<!-- Operations&#45;&gt;MARGIN -->
<g id="edge17" class="edge">
<title>Operations&#45;&gt;MARGIN</title>
<path fill="none" stroke="orange" stroke-width="2" d="M793.52,-594C858.85,-594 973,-594 973,-594 973,-594 973,-78.19 973,-78.19"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="976.5,-78.19 973,-68.19 969.5,-78.19 976.5,-78.19"/>
</g>
<!-- Outbound_Logistics&#45;&gt;Marketing_and_Sales -->
<g id="edge3" class="edge">
<title>Outbound_Logistics&#45;&gt;Marketing_and_Sales</title>
<path fill="none" stroke="darkgreen" stroke-width="2" d="M747,-429.72C747,-429.72 747,-356.18 747,-356.18"/>
<polygon fill="darkgreen" stroke="darkgreen" stroke-width="2" points="751.2,-356.18 747,-344.18 742.8,-356.18 751.2,-356.18"/>
</g>
<!-- Outbound_Logistics&#45;&gt;MARGIN -->
<g id="edge18" class="edge">
<title>Outbound_Logistics&#45;&gt;MARGIN</title>
<path fill="none" stroke="orange" stroke-width="2" d="M797.59,-456C854.92,-456 940.5,-456 940.5,-456 940.5,-456 940.5,-61.07 940.5,-61.07"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="944,-61.07 940.5,-51.07 937,-61.07 944,-61.07"/>
</g>
<!-- Marketing_and_Sales&#45;&gt;Service -->
<g id="edge4" class="edge">
<title>Marketing_and_Sales&#45;&gt;Service</title>
<path fill="none" stroke="darkgreen" stroke-width="2" d="M730.25,-291.72C730.25,-291.72 730.25,-218.18 730.25,-218.18"/>
<polygon fill="darkgreen" stroke="darkgreen" stroke-width="2" points="734.45,-218.18 730.25,-206.18 726.05,-218.18 734.45,-218.18"/>
</g>
<!-- Marketing_and_Sales&#45;&gt;MARGIN -->
<g id="edge19" class="edge">
<title>Marketing_and_Sales&#45;&gt;MARGIN</title>
<path fill="none" stroke="orange" stroke-width="2" d="M782.25,-291.82C782.25,-223.49 782.25,-45 782.25,-45 782.25,-45 918.76,-45 918.76,-45"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="918.76,-48.5 928.76,-45 918.76,-41.5 918.76,-48.5"/>
</g>
<!-- Service&#45;&gt;MARGIN -->
<g id="edge20" class="edge">
<title>Service&#45;&gt;MARGIN</title>
<path fill="none" stroke="orange" stroke-width="2" d="M715,-153.91C715,-109.03 715,-22 715,-22 715,-22 920.69,-22 920.69,-22"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="920.69,-25.5 930.69,-22 920.69,-18.5 920.69,-25.5"/>
</g>
</g>
</svg>
