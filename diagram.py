# pip install graphviz
from graphviz import Digraph

# ---------- Enhanced Value Chain Model ----------
primary_activities = {
    "Inbound Logistics": ["Receiving", "Warehousing", "Inventory Control"],
    "Operations": ["Manufacturing", "Assembly", "Quality Control"],
    "Outbound Logistics": ["Order Processing", "Shipping", "Distribution"],
    "Marketing & Sales": ["Advertising", "Sales Force", "Channel Relations"],
    "Service": ["Installation", "Repair", "Customer Support"]
}

support_activities = {
    "Firm Infrastructure": ["Planning", "Finance", "Legal", "Quality Management"],
    "HR Management": ["Recruiting", "Training", "Development", "Compensation"],
    "Technology Development": ["R&D", "Product Design", "Process Automation"],
    "Procurement": ["Purchasing", "Supplier Relations", "Material Management"]
}

# ---------- Create Enhanced Graph ----------
g = Digraph("Enhanced_ValueChain", filename="enhanced_value_chain.gv", format="svg")
g.attr(rankdir="TB", splines="ortho", nodesep="0.8", ranksep="1.2")
g.attr('graph', bgcolor='white', fontname='Arial', fontsize='14')

# Support Activities (Top Section)
with g.subgraph(name="cluster_support") as c:
    c.attr(label="Support Activities", style="filled", color="lightblue", fontsize="16", fontname="Arial Bold")

    for activity, details in support_activities.items():
        # Create main support activity node
        c.node(activity,
               label=f"{activity}\\n• " + "\\n• ".join(details),
               shape="box",
               style="filled,rounded",
               fillcolor="#E8F4FD",
               fontname="Arial",
               fontsize="10")

# Primary Activities (Bottom Section)
with g.subgraph(name="cluster_primary") as c:
    c.attr(label="Primary Activities", style="filled", color="lightgreen", fontsize="16", fontname="Arial Bold")

    # Create primary activity nodes
    primary_nodes = []
    for activity, details in primary_activities.items():
        node_name = activity.replace(" ", "_").replace("&", "and")
        primary_nodes.append(node_name)

        c.node(node_name,
               label=f"{activity}\\n• " + "\\n• ".join(details),
               shape="box",
               style="filled,rounded",
               fillcolor="#E8F8E8",
               fontname="Arial",
               fontsize="10")

    # Connect primary activities in sequence
    for i in range(len(primary_nodes) - 1):
        c.edge(primary_nodes[i], primary_nodes[i + 1],
               color="darkgreen", penwidth="2", arrowsize="1.2")

# Add value flow connections from support to primary
support_to_primary = {
    "Firm Infrastructure": ["Inbound_Logistics", "Outbound_Logistics"],
    "HR Management": ["Operations", "Service"],
    "Technology Development": ["Operations", "Marketing_and_Sales"],
    "Procurement": primary_nodes  # Procurement supports all primary activities
}

for support_act, primary_targets in support_to_primary.items():
    for target in primary_targets:
        g.edge(support_act, target,
               style="dashed",
               color="blue",
               arrowsize="0.8",
               constraint="false")

# Add margin/profit indicator
g.node("MARGIN",
       label="MARGIN\\n(Profit)",
       shape="diamond",
       style="filled",
       fillcolor="gold",
       fontname="Arial Bold",
       fontsize="12")

# Connect to margin
for node in primary_nodes:
    g.edge(node, "MARGIN",
           color="orange",
           penwidth="2",
           style="bold")

g.render()

# Also create the original simple version
g_simple = Digraph("Simple_ValueChain", filename="simple_value_chain.gv", format="svg")
g_simple.attr(rankdir="LR", splines="ortho", nodesep="0.6", ranksep="1")

# Original simple model
primary = [
    "Inbound Logistics", "Operations",
    "Outbound Logistics", "Marketing & Sales", "Service"
]
support = {
    "Firm Infrastructure": ["Inbound Logistics", "Service"],
    "HR Management": ["Operations"],
    "Technology": ["Operations", "Marketing & Sales"],
    "Procurement": primary,   # touches all primary activities
}

# primary lane
with g_simple.subgraph(name="cluster_primary") as c:
    c.attr(label="Primary Activities", color="lightgrey")
    for p in primary:
        c.node(p, shape="box", style="filled", fillcolor="#ace3ff")
    for left, right in zip(primary, primary[1:]):
        c.edge(left, right)

# support lane
with g_simple.subgraph(name="cluster_support") as c:
    c.attr(label="Support Activities", color="white")
    for s in support:
        c.node(s, shape="ellipse", style="filled", fillcolor="#ffe6a9")

# support links
for s, targets in support.items():
    for t in targets:
        g_simple.edge(s, t, style="dashed")

g_simple.render()
