# pip install graphviz
from graphviz import Digraph

# ---------- model ----------
primary = [
    "Inbound Logistics", "Operations",
    "Outbound Logistics", "Marketing & Sales", "Service"
]
support = {
    "Firm Infrastructure": ["Inbound Logistics", "Service"],
    "HR Management": ["Operations"],
    "Technology": ["Operations", "Marketing & Sales"],
    "Procurement": primary,   # touches all primary activities
}

# ---------- graph ----------
g = Digraph("ValueChain", filename="value_chain.gv", format="svg")
g.attr(rankdir="LR", splines="ortho", nodesep="0.6", ranksep="1")

# primary lane
with g.subgraph(name="cluster_primary") as c:
    c.attr(label="Primary Activities", color="lightgrey")
    for p in primary:
        c.node(p, shape="box", style="filled", fillcolor="#ace3ff")
    for left, right in zip(primary, primary[1:]):
        c.edge(left, right)

# support lane
with g.subgraph(name="cluster_support") as c:
    c.attr(label="Support Activities", color="white")
    for s in support:
        c.node(s, shape="ellipse", style="filled", fillcolor="#ffe6a9")

# support links
for s, targets in support.items():
    for t in targets:
        g.edge(s, t, style="dashed")

g.render()
