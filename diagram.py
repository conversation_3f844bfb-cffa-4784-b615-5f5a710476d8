# pip install graphviz
from graphviz import Digraph

# ---------- Orange Processing Value Chain (Based on Hand-drawn Diagram) ----------
def create_orange_value_chain():
    """Creates the orange processing value chain as shown in the hand-drawn diagram"""

    g = Digraph("Orange_Value_Chain", filename="orange_value_chain", format="png")
    g.attr(rankdir="TB", splines="ortho", nodesep="0.8", ranksep="1.0")
    g.attr('graph', bgcolor='white', fontname='Arial', fontsize='12', dpi='300')
    g.attr('node', fontname='Arial', fontsize='10', style='filled,rounded', shape='box')
    g.attr('edge', fontname='Arial', fontsize='9')

    # Define node styles
    primary_style = {'fillcolor': '#E8F4FD', 'color': 'black'}
    process_style = {'fillcolor': '#F0F8E8', 'color': 'black'}
    product_style = {'fillcolor': '#FFF2E8', 'color': 'black'}

    # Level 1: Planning
    g.node("Planning", **primary_style)

    # Level 2: Pre-planting activities
    g.node("Weeding", **process_style)
    g.node("Application_of_Fertilizer", "Application\nof Fertilizer", **process_style)
    g.node("Cut_Orange_Tree_Branches", "Cut Orange Tree\nBranches", **process_style)
    g.node("Application_of_Pesticides", "Application\nof Pesticides", **process_style)

    # Level 3: Harvesting
    g.node("Harvesting", **primary_style)

    # Level 4: Post-harvest processing
    g.node("Orange", **product_style)
    g.node("Orange_Leaves", "Orange\nLeaves", **product_style)
    g.node("Natural_Insect_Repellent", "Natural Insect\nRepellent", **product_style)

    # Level 5: Processing stages
    g.node("Picking", **process_style)
    g.node("Tea_Leaves", "Tea Leaves", **product_style)
    g.node("Packaging_1", "Packaging", **process_style)

    # Level 6: Orange processing
    g.node("Household_Consumption", "Household\nConsumption", **product_style)
    g.node("Industrial_Application", "Industrial\nApplication", **process_style)
    g.node("Peeling", **process_style)
    g.node("Orange_Fruit", "Orange Fruit", **product_style)

    # Level 7: Further processing
    g.node("Peels", **product_style)
    g.node("Peel_Powder", "Peel\nPowder", **product_style)
    g.node("Juice", **product_style)
    g.node("Pulp", **product_style)
    g.node("Seeds", **product_style)

    # Level 8: Final products
    g.node("Essential_Oil", "Essential\nOil", **product_style)
    g.node("Pectin", **product_style)
    g.node("Powder", **product_style)
    g.node("Oil", **product_style)

    # Level 9: Final packaging
    g.node("Packaging_2", "Packaging", **process_style)

    # Create connections based on the hand-drawn diagram

    # From Planning to pre-planting activities
    g.edge("Planning", "Weeding")
    g.edge("Planning", "Application_of_Fertilizer")
    g.edge("Planning", "Cut_Orange_Tree_Branches")
    g.edge("Planning", "Application_of_Pesticides")

    # From pre-planting to harvesting
    g.edge("Application_of_Fertilizer", "Harvesting")

    # From harvesting to products
    g.edge("Harvesting", "Orange")
    g.edge("Harvesting", "Orange_Leaves")
    g.edge("Orange_Leaves", "Natural_Insect_Repellent")

    # Orange processing path
    g.edge("Orange", "Picking")
    g.edge("Orange_Leaves", "Tea_Leaves")
    g.edge("Tea_Leaves", "Packaging_1")

    # From picking
    g.edge("Picking", "Household_Consumption")
    g.edge("Picking", "Industrial_Application")
    g.edge("Picking", "Peeling")
    g.edge("Peeling", "Orange_Fruit")

    # From peeling to products
    g.edge("Peeling", "Peels")
    g.edge("Orange_Fruit", "Peel_Powder")
    g.edge("Orange_Fruit", "Juice")
    g.edge("Orange_Fruit", "Pulp")
    g.edge("Orange_Fruit", "Seeds")

    # Final products
    g.edge("Peels", "Essential_Oil")
    g.edge("Peels", "Pectin")
    g.edge("Pulp", "Powder")
    g.edge("Seeds", "Oil")

    # Final packaging
    g.edge("Powder", "Packaging_2")
    g.edge("Oil", "Packaging_2")

    return g

def create_word_compatible_diagram():
    """Creates a high-resolution PNG suitable for Word documents"""

    # Create the orange value chain
    orange_chain = create_orange_value_chain()

    # Render as high-resolution PNG for Word compatibility
    orange_chain.render(format='png', cleanup=True)

    print("Orange value chain diagram created successfully!")
    print("Files generated:")
    print("- orange_value_chain.png (for Word document)")
    print("- orange_value_chain (Graphviz source)")

    return orange_chain

# Create a version that exactly matches the hand-drawn diagram layout and flow directions
def create_exact_match_diagram():
    """Creates a diagram that exactly matches the hand-drawn image layout and flow directions"""

    g = Digraph("Orange_Exact", filename="orange_exact_match", format="png")
    g.attr(rankdir="TB", splines="ortho", nodesep="1.2", ranksep="0.8")
    g.attr('graph', bgcolor='white', fontname='Arial', fontsize='12', dpi='300')
    g.attr('node', fontname='Arial', fontsize='10', style='filled', shape='box',
           fillcolor='lightblue', color='black', width='1.8', height='0.7')
    g.attr('edge', fontname='Arial', fontsize='9', color='black', arrowsize='0.8')

    # Define nodes with exact positioning using subgraphs for layout control

    # Level 1: Planting (top)
    g.node("Planting", fillcolor='#E8F4FD')

    # Level 2: Pre-planting activities (horizontal row)
    with g.subgraph() as s:
        s.attr(rank='same')
        s.node("Weeding", fillcolor='#F0F8E8')
        s.node("Application_of_Fertilizer", "Application\nof Fertilizer", fillcolor='#F0F8E8')
        s.node("Cut_Orange_Tree_Branches", "Cut Orange Tree\nBranches", fillcolor='#F0F8E8')
        s.node("Application_of_Pesticides", "Application\nof Pesticides", fillcolor='#F0F8E8')

    # Level 3: Harvesting
    g.node("Harvesting", fillcolor='#E8F4FD')

    # Level 4: Primary products (horizontal row)
    with g.subgraph() as s:
        s.attr(rank='same')
        s.node("Orange", fillcolor='#FFF2E8')
        s.node("Orange_Leaves", "Orange\nLeaves", fillcolor='#FFF2E8')
        s.node("Natural_Insect_Repellent", "Natural Insect\nRepellent", fillcolor='#FFF2E8')

    # Level 5: Processing stage 1
    with g.subgraph() as s:
        s.attr(rank='same')
        s.node("Picking", fillcolor='#F0F8E8')
        s.node("Tea_Leaves", "Tea Leaves", fillcolor='#FFF2E8')
        s.node("Packaging_1", "Packaging", fillcolor='#F0F8E8')

    # Level 6: Processing stage 2 (horizontal row)
    with g.subgraph() as s:
        s.attr(rank='same')
        s.node("Household_Consumption", "Household\nConsumption", fillcolor='#FFF2E8')
        s.node("Industrial_Application", "Industrial\nApplication", fillcolor='#F0F8E8')
        s.node("Peeling", fillcolor='#F0F8E8')
        s.node("Orange_Fruit", "Orange Fruit", fillcolor='#FFF2E8')

    # Level 7: Products from orange fruit (horizontal row)
    with g.subgraph() as s:
        s.attr(rank='same')
        s.node("Peels", fillcolor='#FFF2E8')
        s.node("Peel_Powder", "Peel\nPowder", fillcolor='#FFF2E8')
        s.node("Juice", fillcolor='#FFF2E8')
        s.node("Pulp", fillcolor='#FFF2E8')
        s.node("Seeds", fillcolor='#FFF2E8')

    # Level 8: Final products (horizontal row)
    with g.subgraph() as s:
        s.attr(rank='same')
        s.node("Essential_Oil", "Essential\nOil", fillcolor='#F3E5F5')
        s.node("Pectin", fillcolor='#F3E5F5')
        s.node("Powder", fillcolor='#F3E5F5')
        s.node("Oil", fillcolor='#F3E5F5')

    # Level 9: Final packaging
    g.node("Packaging_2", "Packaging", fillcolor='#F0F8E8')

    # Create connections exactly as shown in the hand-drawn diagram

    # From Planting to all pre-planting activities
    g.edge("Planting", "Weeding")
    g.edge("Planting", "Application_of_Fertilizer")
    g.edge("Planting", "Cut_Orange_Tree_Branches")
    g.edge("Planting", "Application_of_Pesticides")

    # Only fertilizer leads to harvesting (as shown in image)
    g.edge("Application_of_Fertilizer", "Harvesting")

    # From harvesting to products
    g.edge("Harvesting", "Orange")
    g.edge("Harvesting", "Orange_Leaves")

    # Orange leaves processing path
    g.edge("Orange_Leaves", "Natural_Insect_Repellent")
    g.edge("Orange_Leaves", "Tea_Leaves")
    g.edge("Tea_Leaves", "Packaging_1")

    # Orange processing path
    g.edge("Orange", "Picking")

    # From picking to different uses
    g.edge("Picking", "Household_Consumption")
    g.edge("Picking", "Industrial_Application")
    g.edge("Picking", "Peeling")

    # From peeling to orange fruit
    g.edge("Peeling", "Orange_Fruit")

    # From orange fruit to all products (as shown with multiple arrows in image)
    g.edge("Orange_Fruit", "Peels")
    g.edge("Orange_Fruit", "Peel_Powder")
    g.edge("Orange_Fruit", "Juice")
    g.edge("Orange_Fruit", "Pulp")
    g.edge("Orange_Fruit", "Seeds")

    # From products to final products
    g.edge("Peels", "Essential_Oil")
    g.edge("Peels", "Pectin")
    g.edge("Pulp", "Powder")
    g.edge("Seeds", "Oil")

    # Final packaging (as shown in image)
    g.edge("Powder", "Packaging_2")
    g.edge("Oil", "Packaging_2")

    g.render(format='png', cleanup=True)
    return g

# Create a simplified version that matches the hand-drawn diagram more closely
def create_simplified_orange_chain():
    """Creates a simplified version that closely matches the hand-drawn diagram"""

    return create_exact_match_diagram()  # Use the exact match version

if __name__ == "__main__":
    # Create both versions
    print("Creating Orange Value Chain Diagrams...")

    # Create the detailed version
    detailed_chain = create_word_compatible_diagram()

    # Create the exact match version
    exact_chain = create_exact_match_diagram()

    # Create the simplified version (which now uses exact match)
    simple_chain = create_simplified_orange_chain()

    print("\nExact match orange value chain created!")
    print("Files generated:")
    print("- orange_exact_match.png (exact match to hand-drawn image)")
    print("- orange_value_chain.png (detailed version)")
    print("- orange_simple.png (same as exact match)")

    print("\nTo insert into Word document:")
    print("1. Open Microsoft Word")
    print("2. Go to Insert > Pictures > This Device")
    print("3. Select 'orange_exact_match.png' for the most accurate version")
    print("4. Resize as needed in Word")
