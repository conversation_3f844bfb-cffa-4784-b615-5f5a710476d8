digraph ValueChain {
	nodesep=0.6 rankdir=LR ranksep=1 splines=ortho
	subgraph cluster_primary {
		color=lightgrey label="Primary Activities"
		"Inbound Logistics" [fillcolor="#ace3ff" shape=box style=filled]
		Operations [fillcolor="#ace3ff" shape=box style=filled]
		"Outbound Logistics" [fillcolor="#ace3ff" shape=box style=filled]
		"Marketing & Sales" [fillcolor="#ace3ff" shape=box style=filled]
		Service [fillcolor="#ace3ff" shape=box style=filled]
		"Inbound Logistics" -> Operations
		Operations -> "Outbound Logistics"
		"Outbound Logistics" -> "Marketing & Sales"
		"Marketing & Sales" -> Service
	}
	subgraph cluster_support {
		color=white label="Support Activities"
		"Firm Infrastructure" [fillcolor="#ffe6a9" shape=ellipse style=filled]
		"HR Management" [fillcolor="#ffe6a9" shape=ellipse style=filled]
		Technology [fillcolor="#ffe6a9" shape=ellipse style=filled]
		Procurement [fillcolor="#ffe6a9" shape=ellipse style=filled]
	}
	"Firm Infrastructure" -> "Inbound Logistics" [style=dashed]
	"Firm Infrastructure" -> Service [style=dashed]
	"HR Management" -> Operations [style=dashed]
	Technology -> Operations [style=dashed]
	Technology -> "Marketing & Sales" [style=dashed]
	Procurement -> "Inbound Logistics" [style=dashed]
	Procurement -> Operations [style=dashed]
	Procurement -> "Outbound Logistics" [style=dashed]
	Procurement -> "Marketing & Sales" [style=dashed]
	Procurement -> Service [style=dashed]
}
