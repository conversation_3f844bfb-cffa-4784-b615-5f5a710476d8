#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a Word document with the Orange Value Chain diagram
that matches the hand-drawn image provided by the user.
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_SECTION
import os

def create_orange_value_chain_word():
    """Creates a Word document with the orange value chain diagram"""
    
    # Create a new Document
    doc = Document()
    
    # Set up the document
    section = doc.sections[0]
    section.page_height = Inches(11)
    section.page_width = Inches(8.5)
    section.left_margin = Inches(1)
    section.right_margin = Inches(1)
    section.top_margin = Inches(1)
    section.bottom_margin = Inches(1)
    
    # Add title
    title = doc.add_heading('Orange Processing Value Chain', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add a subtitle
    subtitle = doc.add_paragraph('Complete Flow from Planting to Final Products')
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle_format = subtitle.runs[0].font
    subtitle_format.size = Pt(14)
    subtitle_format.italic = True
    
    # Add some space
    doc.add_paragraph()
    
    # Check if the PNG file exists and add it
    png_files = ['orange_simple.png', 'orange_value_chain.png']
    image_added = False
    
    for png_file in png_files:
        if os.path.exists(png_file):
            # Add the diagram image
            paragraph = doc.add_paragraph()
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add the image - resize to fit the page width
            run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
            try:
                run.add_picture(png_file, width=Inches(6.5))
                image_added = True
                print(f"Added image: {png_file}")
                break
            except Exception as e:
                print(f"Could not add image {png_file}: {e}")
                continue
    
    if not image_added:
        # If no image could be added, create a text-based representation
        doc.add_paragraph("Image file not found. Creating text-based diagram:")
        
        # Add text-based flow
        flow_text = """
        PLANTING
        ↓
        Weeding → Application of Fertilizer → Cut Orange Tree Branches → Application of Pesticides
        ↓
        HARVESTING
        ↓
        Orange → Orange Leaves
        ↓         ↓
        Picking   Tea Leaves → Packaging
        ↓         ↓
        Household Consumption | Industrial Application | Peeling
                                                      ↓
                                                   Orange Fruit
                                                      ↓
        Peels → Peel Powder → Juice → Pulp → Seeds
        ↓                              ↓      ↓
        Essential Oil | Pectin         Powder  Oil
                                       ↓      ↓
                                      Packaging
        """
        
        flow_paragraph = doc.add_paragraph(flow_text)
        flow_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        flow_format = flow_paragraph.runs[0].font
        flow_format.name = 'Courier New'
        flow_format.size = Pt(10)
    
    # Add description section
    doc.add_page_break()
    
    # Add description heading
    desc_heading = doc.add_heading('Value Chain Description', level=1)
    
    # Add detailed description
    description_text = """
    This orange processing value chain diagram illustrates the complete flow from agricultural 
    planning through to final product packaging. The chain demonstrates how a single agricultural 
    input (orange farming) can generate multiple revenue streams through different processing paths.
    
    Key Stages:
    
    1. PLANNING PHASE
       • Weeding - Land preparation and weed control
       • Application of Fertilizer - Soil nutrient management
       • Cut Orange Tree Branches - Pruning for optimal growth
       • Application of Pesticides - Pest and disease control
    
    2. HARVESTING PHASE
       • Collection of oranges and orange leaves
       • Separation of different plant parts for various uses
    
    3. PROCESSING PHASE
       • Orange fruit processing for juice, pulp, and seeds
       • Orange leaf processing for tea and natural repellents
       • Peel processing for essential oils and pectin
    
    4. FINAL PRODUCTS
       • Food products: Juice, pulp powder
       • Industrial products: Essential oil, pectin
       • Agricultural products: Natural insect repellent
       • Beverage products: Tea leaves
       • Edible oil: Seed oil
    
    This value chain maximizes the utilization of all parts of the orange plant, creating 
    multiple income streams and reducing waste through comprehensive processing.
    """
    
    desc_paragraph = doc.add_paragraph(description_text)
    desc_format = desc_paragraph.runs[0].font
    desc_format.size = Pt(11)
    
    # Save the document
    filename = 'Orange_Value_Chain_Diagram.docx'
    doc.save(filename)
    
    print(f"\nWord document created successfully: {filename}")
    print("The document contains:")
    print("- Title page with the orange value chain diagram")
    print("- Detailed description of the value chain process")
    print("- Professional formatting suitable for presentations or reports")
    
    return filename

if __name__ == "__main__":
    try:
        # First, make sure we have the required library
        print("Creating Word document with Orange Value Chain diagram...")
        filename = create_orange_value_chain_word()
        print(f"\n✅ Success! Open '{filename}' in Microsoft Word to view the diagram.")
        
    except ImportError as e:
        print("❌ Error: python-docx library not found.")
        print("Please install it using: pip install python-docx")
        print("Then run this script again.")
        
    except Exception as e:
        print(f"❌ Error creating Word document: {e}")
        print("Please check that you have write permissions in this directory.")
